package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.core.generated.model.v1.VideoSessionInitResponseStageRangesInner;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * VideoSessionInitResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class VideoSessionInitResponse {

  private String sessionId;

  private String reportKey;

  private Boolean hasReward;

  @Valid
  private List<@Valid VideoSessionInitResponseStageRangesInner> stageRanges;

  /**
   * Default constructor
   * @deprecated Use {@link VideoSessionInitResponse#VideoSessionInitResponse(String, Boolean)}
   */
  @Deprecated
  public VideoSessionInitResponse() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public VideoSessionInitResponse(String sessionId, Boolean hasReward) {
    this.sessionId = sessionId;
    this.hasReward = hasReward;
  }

  public VideoSessionInitResponse sessionId(String sessionId) {
    this.sessionId = sessionId;
    return this;
  }

  /**
   * 视频观看会话ID
   * @return sessionId
  */
  @NotNull 
  @Schema(name = "sessionId", description = "视频观看会话ID", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("sessionId")
  public String getSessionId() {
    return sessionId;
  }

  public void setSessionId(String sessionId) {
    this.sessionId = sessionId;
  }

  public VideoSessionInitResponse reportKey(String reportKey) {
    this.reportKey = reportKey;
    return this;
  }

  /**
   * 报告key
   * @return reportKey
  */
  
  @Schema(name = "reportKey", description = "报告key", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("reportKey")
  public String getReportKey() {
    return reportKey;
  }

  public void setReportKey(String reportKey) {
    this.reportKey = reportKey;
  }

  public VideoSessionInitResponse hasReward(Boolean hasReward) {
    this.hasReward = hasReward;
    return this;
  }

  /**
   * 该视频是否有奖励
   * @return hasReward
  */
  @NotNull 
  @Schema(name = "hasReward", description = "该视频是否有奖励", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("hasReward")
  public Boolean getHasReward() {
    return hasReward;
  }

  public void setHasReward(Boolean hasReward) {
    this.hasReward = hasReward;
  }

  public VideoSessionInitResponse stageRanges(List<@Valid VideoSessionInitResponseStageRangesInner> stageRanges) {
    this.stageRanges = stageRanges;
    return this;
  }

  public VideoSessionInitResponse addStageRangesItem(VideoSessionInitResponseStageRangesInner stageRangesItem) {
    if (this.stageRanges == null) {
      this.stageRanges = new ArrayList<>();
    }
    this.stageRanges.add(stageRangesItem);
    return this;
  }

  /**
   * 观看进度阶段区间定义
   * @return stageRanges
  */
  @Valid 
  @Schema(name = "stageRanges", description = "观看进度阶段区间定义", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("stageRanges")
  public List<@Valid VideoSessionInitResponseStageRangesInner> getStageRanges() {
    return stageRanges;
  }

  public void setStageRanges(List<@Valid VideoSessionInitResponseStageRangesInner> stageRanges) {
    this.stageRanges = stageRanges;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VideoSessionInitResponse videoSessionInitResponse = (VideoSessionInitResponse) o;
    return Objects.equals(this.sessionId, videoSessionInitResponse.sessionId) &&
        Objects.equals(this.reportKey, videoSessionInitResponse.reportKey) &&
        Objects.equals(this.hasReward, videoSessionInitResponse.hasReward) &&
        Objects.equals(this.stageRanges, videoSessionInitResponse.stageRanges);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sessionId, reportKey, hasReward, stageRanges);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VideoSessionInitResponse {\n");
    sb.append("    sessionId: ").append(toIndentedString(sessionId)).append("\n");
    sb.append("    reportKey: ").append(toIndentedString(reportKey)).append("\n");
    sb.append("    hasReward: ").append(toIndentedString(hasReward)).append("\n");
    sb.append("    stageRanges: ").append(toIndentedString(stageRanges)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

