package com.drex.web.core.delegate;

import com.drex.asset.api.model.AssetDTO;
import com.drex.asset.api.model.constant.AssetType;
import com.drex.asset.api.model.request.AssetQueryRequest;
import com.drex.asset.api.service.RemoteAssetService;
import com.drex.core.api.RemoteRexyBasketService;
import com.drex.core.api.RemoteRexyService;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.*;
import com.drex.core.api.response.*;
import com.drex.customer.api.response.PassportDTO;
import com.drex.endpoint.api.RemoteTransactionsService;
import com.drex.web.common.PassportHolder;
import com.drex.web.core.converter.CoreConverter;
import com.drex.web.core.generated.api.v1.RexyApiDelegate;
import com.drex.web.core.generated.model.v1.*;
import com.drex.web.core.generated.model.v1.RewardCollectResponse;
import com.kikitrade.framework.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class RexyApiDelegateImpl implements RexyApiDelegate {

    @DubboReference
    private RemoteRexyService remoteRexyService;
    @DubboReference
    private RemoteRexyBasketService remoteRexyBasketService;
    @DubboReference
    private RemoteTransactionsService remoteTransactionsService;
    @DubboReference
    private RemoteAssetService remoteAssetService;

    /**
     * GET /rexys : 获取Rexys信息
     * 获取Rexys信息
     *

    /**
     * POST /rexys/claim : 领取积分奖励
     * 领取积分奖励
     *
     * @param rexyClaimRequest (required)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<ClaimResponse> rexyClaim(RexyClaimRequest rexyClaimRequest) {
        log.info("claim rexy request: {}", rexyClaimRequest);
        ClaimMaizeRequest request = new ClaimMaizeRequest();
        request.setCustomerId(PassportHolder.passport().getPassportId());
        request.setTransactionHash(rexyClaimRequest.getTransactionHash());
        request.setCallData(rexyClaimRequest.getCallData());
        Response<MaizeKernelDTO> maizeKernelDTOResponse = remoteRexyBasketService.claimMaizeKernel(request);
        log.info("claim maize kernel result: {}", maizeKernelDTOResponse);
        ClaimResponse response = new ClaimResponse();
        if(maizeKernelDTOResponse.isSuccess()){
            response.success();
            ClaimResponseAllOfObj obj = new ClaimResponseAllOfObj();
            obj.setAmount(maizeKernelDTOResponse.getData().getReceived().toString());
            response.setObj(obj);
            return ResponseEntity.ok(response);
        }
        response.fail(maizeKernelDTOResponse);
        return ResponseEntity.badRequest().body(response);
    }


    /**
     * POST /rexys/claim/build/{basketType} : 生成交易业务id
     * 生成交易业务id
     *
     * @param basketType 交易业务场景(normal, invite) (required)
     * @param address    钱包地址 (optional)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<ClaimBuildResponse> rexyClaimBuild(String basketType, String address) {
        log.info("rexyClaimBuild request: basketType={}, address={}", basketType, address);
        Response<WalletOperationDTO> walletOperationDTOResponse = remoteRexyBasketService.buildClaimRequests(PassportHolder.passport().getPassportId(),
                address == null ? (PassportHolder.passport().getAddress()) : address,
                basketType);
        log.info("rexyClaimBuild result: {}", walletOperationDTOResponse);
        ClaimBuildResponse response = new ClaimBuildResponse();
        if(walletOperationDTOResponse.isSuccess()){
            response.success();
            ClaimBuildResponseAllOfObj obj = new ClaimBuildResponseAllOfObj();
            obj.setTargetAddress(walletOperationDTOResponse.getData().getTargetAddress());
            obj.setCallData(walletOperationDTOResponse.getData().getCallData());
            obj.setAmount(walletOperationDTOResponse.getData().getValue().toString());
            response.setObj(obj);
            return ResponseEntity.ok(response);
        }
        response.fail(walletOperationDTOResponse);
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * POST /rexys/reward/collect : 恐龙收集奖励
     * 恐龙收集奖励
     *
     * @param rexyRewardCollectRequest (optional)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<RewardCollectResponse> rexyRewardCollect(RexyRewardCollectRequest rexyRewardCollectRequest) {
        log.info("rexyRewardCollect request: {}", rexyRewardCollectRequest);
        CollectMaizeRequest request = new CollectMaizeRequest();
        request.setCustomerId(PassportHolder.passport().getPassportId());
        request.setMaizeCode(rexyRewardCollectRequest.getRewardId());
        Response<MaizeDTO> response = remoteRexyService.collectMaize(request);
        log.info("rexyRewardCollect result: {}", response);
        RewardCollectResponse rewardCollectResponse = new RewardCollectResponse();
        if(response.isSuccess() && response.getData() != null){
            MaizeDTO maizeDTO = response.getData();
            rewardCollectResponse.success();
            rewardCollectResponse.setRexyRewardId(maizeDTO.getCode());
            rewardCollectResponse.setRexyRewardLevel(maizeDTO.getLevel());
            rewardCollectResponse.setRexyRewardPoint(maizeDTO.getScore().toString());
            return ResponseEntity.ok(rewardCollectResponse);
        }
        rewardCollectResponse.fail(response);
        return ResponseEntity.ok(rewardCollectResponse);
    }

    /**
     * GET /rexys/reward/last : 查询最新恐龙蛋
     * 查询最新恐龙蛋
     *
     * @param platform 平台x or youtube (required)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<RexyRewardLastResponse> rexyRewardLast(String platform, String contentId) {
        LastMaizeRequest request = new LastMaizeRequest();
        request.setSocialPlatform(SocialConstant.PlatformEnum.valueOf(platform));
        request.setCustomerId(PassportHolder.passport().getPassportId());
        request.setContentId(contentId);
        Response<List<MaizeDTO>> listResponse = remoteRexyService.lastMaize(request);
        if (!listResponse.isSuccess()) {
            RexyRewardLastResponse response = new RexyRewardLastResponse();
            response.fail(listResponse.getCode(), listResponse.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
        RexyRewardLastResponse response = new RexyRewardLastResponse();
        response.success();

        List<MaizeDTO> data = listResponse.getData();
        if(CollectionUtils.isEmpty(data)){
            return ResponseEntity.ok(response);
        }
        //当前的待领取的奖励
        RexyReward reward = new RexyReward();
        List<RexyRewardItem> rewards = new ArrayList<>();
        for(MaizeDTO maize : data){
            RexyRewardItem rexyReward = new RexyRewardItem();
            rexyReward.setCode(maize.getCode());
            rexyReward.setStage(maize.getProgress());
            rexyReward.setPoint(maize.getScore().toString());
            rexyReward.setStatus(maize.getMaizeStatus().equals(RexyConstant.MaizeStatus.ISSUED) ? RexyRewardItem.StatusEnum.UNCLAIMED : RexyRewardItem.StatusEnum.valueOf(maize.getMaizeStatus().name()));
            rewards.add(rexyReward);
            if(maize.getMaizeStatus().equals(RexyConstant.MaizeStatus.ISSUED)){
                reward.setCode(maize.getCode());
                reward.setStage(maize.getProgress());
                reward.setPoint(maize.getScore().toString());
            }
        }
        reward.setRewards(rewards);
        response.setObj(reward);
        return ResponseEntity.ok(response);
    }

    /**
     * GET /rexys/avatar : 查询恐龙列表
     * 查询恐龙列表
     *
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<RexysResponse> rexys() {

        Response<List<RexysDTO>> listResponse = remoteRexyService.listRexy(PassportHolder.passport().getPassportId());
        if (!listResponse.isSuccess()) {
            RexysResponse response = new RexysResponse();
            response.fail(listResponse.getCode(), listResponse.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
        RexysResponse response = new RexysResponse();
        response.success();
        List<RexysDTO> data = listResponse.getData();
        if(CollectionUtils.isEmpty(data)){
            return ResponseEntity.ok(response);
        }
        response.setObj(CoreConverter.toRexys(data));
        return ResponseEntity.ok(response);
    }

    /**
     * GET /rexys/me : 查询当前用户宠物信息
     * 查询当前用户宠物信息
     *
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<RexyInfoResponse> rexy() {

        PassportDTO passport = PassportHolder.passport();

        RexyInfo rexyInfo = new RexyInfo();
        rexyInfo.setCustomerId(passport.getPassportId());
        rexyInfo.setAvatar(passport.getAvatar());
        AssetQueryRequest assetQueryRequest = AssetQueryRequest.builder()
                .customerId(passport.getPassportId())
                .assetType(AssetType.POINT).build();

        Response<AssetDTO> assetDTOResponse = remoteAssetService.asset(assetQueryRequest);
        if(assetDTOResponse.isSuccess() && assetDTOResponse.getData() != null){
            rexyInfo.setPoint(assetDTOResponse.getData().getAvailable().longValue());
        }
        RexyBasketsRequest request = RexyBasketsRequest.builder()
                .customerId(passport.getPassportId())
                .basketType(RexyConstant.RexyBasketsTypeEnum.normal)
                .build();
        Response<CustomerRexyBasketsDTO> customerRexyBasketsResponse = remoteRexyBasketService.getCustomerRexyBaskets(request);
        if(customerRexyBasketsResponse.isSuccess()){
            CustomerRexyBasketsDTO basketsDTO = customerRexyBasketsResponse.getData();
            RexyInfoRexy rexyInfoRexy = new RexyInfoRexy();
            rexyInfoRexy.setId(basketsDTO.getRexyId());
            rexyInfoRexy.setName(basketsDTO.getRexyName());
            rexyInfoRexy.setAvatar(basketsDTO.getRexyAvatar());
            rexyInfoRexy.setBasketPoint(basketsDTO.getReceived().intValue());
            rexyInfoRexy.setRate(basketsDTO.getBasketRate().intValue());
            rexyInfoRexy.setLimitPoint(basketsDTO.getBasketLimit().intValue());
            rexyInfo.setRexy(rexyInfoRexy);
            rexyInfo.setAvatar(basketsDTO.getRexyAvatar());
        }

        RexyInfoResponse rexyInfoResponse = new RexyInfoResponse();
        rexyInfoResponse.success();
        rexyInfoResponse.setObj(rexyInfo);
        return ResponseEntity.ok(rexyInfoResponse);
    }
}
