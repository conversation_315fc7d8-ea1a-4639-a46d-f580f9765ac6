package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.VideoEventsReportRequest;
import com.drex.web.core.generated.model.v1.VideoSessionInitRequest;
import com.drex.web.core.generated.model.v1.VideoSessionInitResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link VideoApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface VideoApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /video/session/init : 初始化视频观看会话
     * 用户开始观看视频时创建新的会话，返回会话ID及相关奖励信息
     *
     * @param videoSessionInitRequest  (required)
     * @return 会话创建成功 (status code 200)
     *         or 请求参数错误 (status code 400)
     *         or 用户未授权 (status code 401)
     * @see VideoApi#initVideoSession
     */
    default ResponseEntity<VideoSessionInitResponse> initVideoSession(VideoSessionInitRequest videoSessionInitRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"stageRanges\" : [ { \"maxRatio\" : 1.4658129805029452, \"level\" : 0, \"name\" : \"name\", \"rewardAmount\" : \"rewardAmount\", \"minRatio\" : 6.027456183070403 }, { \"maxRatio\" : 1.4658129805029452, \"level\" : 0, \"name\" : \"name\", \"rewardAmount\" : \"rewardAmount\", \"minRatio\" : 6.027456183070403 } ], \"hasReward\" : true, \"sessionId\" : \"sessionId\", \"reportKey\" : \"reportKey\" }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /video/events/report : 上报事件数据
     * 上报事件数据
     *
     * @param videoEventsReportRequest  (optional)
     * @return OK (status code 200)
     * @see VideoApi#reportEvents
     */
    default ResponseEntity<Object> reportEvents(VideoEventsReportRequest videoEventsReportRequest) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
