package com.drex.web.core.delegate;

import com.drex.core.api.RemoteVideoAntiCheatService;
import com.drex.web.core.generated.api.v1.VideoApiDelegate;
import com.drex.web.core.generated.model.v1.VideoEventsReportRequest;
import com.drex.web.core.generated.model.v1.VideoSessionInitRequest;
import com.drex.web.core.generated.model.v1.VideoSessionInitResponse;
import com.drex.web.core.generated.model.v1.VideoSessionInitResponseStageRangesInner;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.common.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class VideoApiDelegateImpl implements VideoApiDelegate {

    @DubboReference
    private RemoteVideoAntiCheatService remoteVideoAntiCheatService;

    @Override
    public ResponseEntity<VideoSessionInitResponse> initVideoSession(VideoSessionInitRequest videoSessionInitRequest) {
        com.drex.core.api.request.VideoSessionInitRequest request = BeanUtil.copyProperties(videoSessionInitRequest, com.drex.core.api.request.VideoSessionInitRequest::new);
        Response<com.drex.core.api.response.VideoSessionInitResponse> response = remoteVideoAntiCheatService.initVideoSession(request);
        VideoSessionInitResponse webResponse = new VideoSessionInitResponse();
        if(response.isSuccess() && response.getData() != null) {
            com.drex.core.api.response.VideoSessionInitResponse data = response.getData();
            webResponse.setSessionId(data.getSessionId());
            webResponse.setReportKey(data.getReportKey());
            webResponse.setHasReward(!CollectionUtils.isEmpty(data.getRewardStages()));
            List<VideoSessionInitResponseStageRangesInner> rewardStages = new ArrayList<>();
            data.getRewardStages().forEach(rewardStage -> {
                VideoSessionInitResponseStageRangesInner rewardStage1 = new VideoSessionInitResponseStageRangesInner();
                rewardStage1.setName(rewardStage.getStageName());
                rewardStage1.setRewardAmount(rewardStage.getRewardAmount());
                rewardStage1.setMinRatio(rewardStage.getRequiredWatchMinPercentage());
                rewardStage1.setMaxRatio(rewardStage.getRequiredWatchMaxPercentage());
                rewardStages.add(rewardStage1);
            });
            webResponse.setStageRanges(rewardStages);
            return ResponseEntity.ok(webResponse);
        }
        webResponse.setHasReward(false);
        return ResponseEntity.ok(webResponse);
    }

    @Override
    public ResponseEntity<Object> reportEvents(VideoEventsReportRequest videoEventsReportRequest) {
        return VideoApiDelegate.super.reportEvents(videoEventsReportRequest);
    }
}
