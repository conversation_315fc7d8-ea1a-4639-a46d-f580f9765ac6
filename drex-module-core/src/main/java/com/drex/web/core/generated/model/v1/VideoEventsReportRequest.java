package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.core.generated.model.v1.EventData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * VideoEventsReportRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class VideoEventsReportRequest {

  private String sessionId;

  private String customerId;

  private String signature;

  private Long clientTimestamp;

  /**
   * 社交平台
   */
  public enum SocialPlatformEnum {
    X("X"),
    
    YOUTUBE("YouTube");

    private String value;

    SocialPlatformEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static SocialPlatformEnum fromValue(String value) {
      for (SocialPlatformEnum b : SocialPlatformEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private SocialPlatformEnum socialPlatform;

  /**
   * 社交事件
   */
  public enum SocialEventEnum {
    REPLAY("replay"),
    
    WATCH("watch");

    private String value;

    SocialEventEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static SocialEventEnum fromValue(String value) {
      for (SocialEventEnum b : SocialEventEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private SocialEventEnum socialEvent;

  @Valid
  private List<@Valid EventData> events = new ArrayList<>();

  private String deviceFinger;

   private String clientIp;

  /**
   * Default constructor
   * @deprecated Use {@link VideoEventsReportRequest#VideoEventsReportRequest(String, String, String, Long, List<@Valid EventData>)}
   */
  @Deprecated
  public VideoEventsReportRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public VideoEventsReportRequest(String sessionId, String customerId, String signature, Long clientTimestamp, List<@Valid EventData> events) {
    this.sessionId = sessionId;
    this.customerId = customerId;
    this.signature = signature;
    this.clientTimestamp = clientTimestamp;
    this.events = events;
  }

  public VideoEventsReportRequest sessionId(String sessionId) {
    this.sessionId = sessionId;
    return this;
  }

  /**
   * 会话ID
   * @return sessionId
  */
  @NotNull 
  @Schema(name = "sessionId", description = "会话ID", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("sessionId")
  public String getSessionId() {
    return sessionId;
  }

  public void setSessionId(String sessionId) {
    this.sessionId = sessionId;
  }

  public VideoEventsReportRequest customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户ID
   * @return customerId
  */
  @NotNull 
  @Schema(name = "customerId", description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("customerId")
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public VideoEventsReportRequest signature(String signature) {
    this.signature = signature;
    return this;
  }

  /**
   * 数据签名
   * @return signature
  */
  @NotNull 
  @Schema(name = "signature", description = "数据签名", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("signature")
  public String getSignature() {
    return signature;
  }

  public void setSignature(String signature) {
    this.signature = signature;
  }

  public VideoEventsReportRequest clientTimestamp(Long clientTimestamp) {
    this.clientTimestamp = clientTimestamp;
    return this;
  }

  /**
   * 客户端时间戳
   * @return clientTimestamp
  */
  @NotNull 
  @Schema(name = "clientTimestamp", description = "客户端时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("clientTimestamp")
  public Long getClientTimestamp() {
    return clientTimestamp;
  }

  public void setClientTimestamp(Long clientTimestamp) {
    this.clientTimestamp = clientTimestamp;
  }

  public VideoEventsReportRequest socialPlatform(SocialPlatformEnum socialPlatform) {
    this.socialPlatform = socialPlatform;
    return this;
  }

  /**
   * 社交平台
   * @return socialPlatform
  */
  
  @Schema(name = "socialPlatform", description = "社交平台", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialPlatform")
  public SocialPlatformEnum getSocialPlatform() {
    return socialPlatform;
  }

  public void setSocialPlatform(SocialPlatformEnum socialPlatform) {
    this.socialPlatform = socialPlatform;
  }

  public VideoEventsReportRequest socialEvent(SocialEventEnum socialEvent) {
    this.socialEvent = socialEvent;
    return this;
  }

  /**
   * 社交事件
   * @return socialEvent
  */
  
  @Schema(name = "socialEvent", description = "社交事件", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialEvent")
  public SocialEventEnum getSocialEvent() {
    return socialEvent;
  }

  public void setSocialEvent(SocialEventEnum socialEvent) {
    this.socialEvent = socialEvent;
  }

  public VideoEventsReportRequest events(List<@Valid EventData> events) {
    this.events = events;
    return this;
  }

  public VideoEventsReportRequest addEventsItem(EventData eventsItem) {
    if (this.events == null) {
      this.events = new ArrayList<>();
    }
    this.events.add(eventsItem);
    return this;
  }

  /**
   * 事件列表
   * @return events
  */
  @NotNull @Valid 
  @Schema(name = "events", description = "事件列表", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("events")
  public List<@Valid EventData> getEvents() {
    return events;
  }

  public void setEvents(List<@Valid EventData> events) {
    this.events = events;
  }

  public VideoEventsReportRequest deviceFinger(String deviceFinger) {
    this.deviceFinger = deviceFinger;
    return this;
  }

  /**
   * 设备指纹
   * @return deviceFinger
  */
  
  @Schema(name = "deviceFinger", description = "设备指纹", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("deviceFinger")
  public String getDeviceFinger() {
    return deviceFinger;
  }

  public void setDeviceFinger(String deviceFinger) {
    this.deviceFinger = deviceFinger;
  }

  public VideoEventsReportRequest clientIp(String clientIp) {
    this.clientIp = clientIp;
    return this;
  }

  /**
   * 客户端IP
   * @return clientIp
  */
  
  @Schema(name = "clientIp", description = "客户端IP", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("clientIp")
  public String getClientIp() {
    return clientIp;
  }

  public void setClientIp(String clientIp) {
    this.clientIp = clientIp;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VideoEventsReportRequest videoEventsReportRequest = (VideoEventsReportRequest) o;
    return Objects.equals(this.sessionId, videoEventsReportRequest.sessionId) &&
        Objects.equals(this.customerId, videoEventsReportRequest.customerId) &&
        Objects.equals(this.signature, videoEventsReportRequest.signature) &&
        Objects.equals(this.clientTimestamp, videoEventsReportRequest.clientTimestamp) &&
        Objects.equals(this.socialPlatform, videoEventsReportRequest.socialPlatform) &&
        Objects.equals(this.socialEvent, videoEventsReportRequest.socialEvent) &&
        Objects.equals(this.events, videoEventsReportRequest.events) &&
        Objects.equals(this.deviceFinger, videoEventsReportRequest.deviceFinger) &&
        Objects.equals(this.clientIp, videoEventsReportRequest.clientIp);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sessionId, customerId, signature, clientTimestamp, socialPlatform, socialEvent, events, deviceFinger, clientIp);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VideoEventsReportRequest {\n");
    sb.append("    sessionId: ").append(toIndentedString(sessionId)).append("\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    signature: ").append(toIndentedString(signature)).append("\n");
    sb.append("    clientTimestamp: ").append(toIndentedString(clientTimestamp)).append("\n");
    sb.append("    socialPlatform: ").append(toIndentedString(socialPlatform)).append("\n");
    sb.append("    socialEvent: ").append(toIndentedString(socialEvent)).append("\n");
    sb.append("    events: ").append(toIndentedString(events)).append("\n");
    sb.append("    deviceFinger: ").append(toIndentedString(deviceFinger)).append("\n");
    sb.append("    clientIp: ").append(toIndentedString(clientIp)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

