openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: 'https://www.apache.org/licenses/LICENSE-2.0'
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
servers:
  - description: kweb server url
    url: 'https://api.trex.dev.dipbit.xyz/v1'
tags:
  - name: Rexy
    description: 恐龙相关接口描述
  - name: News
    description: 新闻相关接口描述
  - name: Video
    description: 视频相关接口描述
paths:
  /news:
    get:
      tags:
        - News
      summary: 查询新闻列表
      description: 查询新闻列表
      operationId: news
      security:
        - jwtBearerAuth: []
      parameters:
        - name: offset
          in: query
          description: 偏移量
          required: false
          schema:
            type: integer
            format: int64
            default: 0
        - name: limit
          in: query
          description: 查询数量
          required: false
          schema:
            type: integer
            format: int64
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: id
                            type:
                              type: string
                              description: 类型 Article、News、YouTube、DApp、XPost
                            name:
                              type: string
                              description: 名称
                            logo:
                              type: string
                              description: logo链接
                            title:
                              type: string
                              description: 标题
                            subTitle:
                              type: string
                              description: 子标题
                            image:
                              type: string
                              description: 图片
                            link:
                              type: string
                              description: 外部链接
                            category:
                              type: array
                              description: 标签
                              items:
                                type: string
                            tag:
                              type: string
                              description: 来源icon图标
                            date:
                              type: integer
                              description: 发布时间
                              format: int64
                            isRecommend:
                              type: boolean
                              description: 是否推荐置顶
                            isReward:
                              type: boolean
                              description: 是否有奖励
  /news/banner:
    get:
      tags:
        - News
      summary: 查询新闻banner
      description: 查询新闻列表banner
      operationId: banner
      security:
        - jwtBearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/paths/~1news/get/responses/200/content/application~1json/schema'
  /notice:
    get:
      tags:
        - Notice
      summary: 查询未读消息通知列表
      description: 查询未读消息通知列表
      operationId: getNotices
      security:
        - jwtBearerAuth: []
      parameters:
        - name: lastFetchNotifyTime
          in: query
          description: 最后一次拉取通知的时间
          required: false
          schema:
            type: integer
            format: int64
            default: 0
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: 通知 id
                            title:
                              type: string
                              description: 标题
                            subTitle:
                              type: string
                              description: 副标题
                            content:
                              type: string
                              description: 内容
                            link:
                              type: string
                              description: 链接
  /rexys/me:
    get:
      tags:
        - Rexy
      summary: 查询当前用户宠物信息
      description: 查询当前用户宠物信息
      operationId: rexy
      security:
        - jwtBearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: object
                        properties:
                          customerId:
                            type: string
                            description: 用户id
                          avatar:
                            type: string
                            description: 头像
                          point:
                            type: integer
                            format: int64
                            description: 积分
                          rexy:
                            type: object
                            properties:
                              id:
                                type: string
                                description: 恐龙id
                              name:
                                type: string
                                description: 恐龙名称
                              basketPoint:
                                type: integer
                                description: 购物车积分
                              limitPoint:
                                type: integer
                                description: 篮子积分上限
                              rate:
                                type: integer
                                description: 当前产生速率
                              avatar:
                                type: string
                                description: 头像
  /rexys/avatar:
    get:
      tags:
        - Rexy
      summary: 查询恐龙列表
      description: 查询恐龙列表
      operationId: rexys
      security:
        - jwtBearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: 恐龙id
                            code:
                              type: string
                              description: 恐龙编号
                            image:
                              type: string
                              description: 图片
                            miniImage:
                              type: string
                              description: 小图
                            level:
                              type: string
                              description: level
                            rate:
                              type: integer
                              description: 生产速率
                            limit:
                              type: integer
                              description: 篮子上限
                            status:
                              type: integer
                              description: '状态, 0:coming soon 1:lock 2:open'
                            has:
                              type: boolean
                              description: 是否拥有
                            selected:
                              type: boolean
                              description: 是否选中
  /rexys/reward/last:
    get:
      tags:
        - Rexy
      summary: 查询最新恐龙蛋
      description: 查询最新恐龙蛋
      operationId: rexyRewardLast
      security:
        - jwtBearerAuth: []
      parameters:
        - name: platform
          in: query
          description: 平台，可选值：X 或 YouTube
          required: true
          schema:
            type: string
            enum:
              - X
              - YouTube
        - name: content_id
          in: query
          description: 内容id
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: object
                        properties:
                          stage:
                            type: integer
                            description: 奖励所属阶段
                          code:
                            type: string
                            description: 恐龙蛋编号，产生奖励后存在
                          point:
                            type: string
                          rewards:
                            type: array
                            items:
                              type: object
                              properties:
                                stage:
                                  type: integer
                                  description: 奖励所属阶段
                                code:
                                  type: string
                                  description: 恐龙蛋编号，产生奖励后存在
                                point:
                                  type: string
                                status:
                                  type: string
                                  format: enum
                                  enum:
                                    - EXPIRED
                                    - CLAIMED
                                    - UNCLAIMED
  /rexys/reward/collect:
    post:
      tags:
        - Rexy
      summary: 恐龙收集奖励
      description: 恐龙收集奖励
      operationId: rexyRewardCollect
      security:
        - jwtBearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                rewardId:
                  type: string
                  description: 奖励ID
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      rexyRewardLevel:
                        type: string
                      rexyRewardId:
                        type: string
                      rexyRewardPoint:
                        type: string
  '/rexys/claim/build/{basketType}':
    post:
      tags:
        - Rexy
      summary: 生成交易业务id
      description: 生成交易业务id
      operationId: rexyClaimBuild
      security:
        - jwtBearerAuth: []
      parameters:
        - name: basketType
          in: path
          description: '交易业务场景(normal, invite)'
          required: true
          schema:
            type: string
        - name: address
          in: query
          description: 钱包地址
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: object
                        properties:
                          targetAddress:
                            type: string
                            description: 目标地址
                          callData:
                            type: string
                            description: 调用数据
                          amount:
                            type: string
                            description: 金额
  /rexys/claim:
    post:
      tags:
        - Rexy
      summary: 领取积分奖励
      description: 领取积分奖励
      operationId: rexyClaim
      security:
        - jwtBearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                transactionHash:
                  type: string
                  description: 交易哈希
                callData:
                  type: string
                  description: 调用数据
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: object
                        properties:
                          amount:
                            type: string
                            description: 金额
  /tx/faucet:
    get:
      tags:
        - Tx
      summary: 水龙头领水
      description: 水龙头领水
      operationId: faucetDrip
      parameters:
        - name: address
          in: query
          description: 领取水的钱包地址
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: object
                        properties:
                          targetAddress:
                            type: string
                            description: 目标地址
                          callData:
                            type: string
                            description: 调用数据
                          amount:
                            type: string
                            description: 金额
                          txHash:
                            type: string
                            description: 交易哈希
  /news/dicovery/blog:
    get:
      tags:
        - News
      summary: discovery blog
      description: discovery blog
      operationId: discoveryBlog
      parameters:
        - name: offset
          in: query
          description: 偏移量
          required: false
          schema:
            type: integer
            format: int64
            default: 0
        - name: limit
          in: query
          description: 查询数量
          required: false
          schema:
            type: integer
            format: int64
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: id
                            title:
                              type: string
                              description: 标题
                            type:
                              type: string
                              description: 子标题，灰色小字
                            summary:
                              type: string
                              description: 摘要
                            link:
                              type: string
                              description: 正文内容链接
                            content:
                              type: string
                              description: 正文内容
                            image:
                              type: string
                              description: 图片
                            isRecommend:
                              type: boolean
                              description: 是否推荐置顶
                            created:
                              type: integer
                              format: int64
                              description: 创建时间
  /news/discovery/events:
    get:
      tags:
        - News
      summary: discovery events
      description: discovery events
      operationId: discoveryEvents
      parameters:
        - name: offset
          in: query
          description: 偏移量
          required: false
          schema:
            type: integer
            format: int64
            default: 0
        - name: limit
          in: query
          description: 查询数量
          required: false
          schema:
            type: integer
            format: int64
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/paths/~1video~1events~1report/post/responses/200/content/application~1json/schema'
                  - type: object
                    properties:
                      obj:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: id
                            title:
                              type: string
                              description: 标题
                            summary:
                              type: string
                              description: 摘要
                            image:
                              type: string
                              description: 图片
                            link:
                              type: string
                              description: 外部链接
                            organizer:
                              type: string
                              description: 组织者
                            location:
                              type: string
                              description: 地点
                            activityStartTime:
                              type: integer
                              format: int64
                              description: 开始时间
                            activityEndTime:
                              type: integer
                              format: int64
                              description: 结束时间
                            remainingTime:
                              type: string
                              description: 剩余时间
  /video/session/init:
    post:
      summary: 初始化视频观看会话
      description: 用户开始观看视频时创建新的会话，返回会话ID及相关奖励信息
      operationId: initVideoSession
      tags:
        - Video
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                videoId:
                  type: string
                  description: 视频标识符
                platform:
                  type: string
                  description: 平台，例如YouTube
              required:
                - videoId
                - platform
      responses:
        '200':
          description: 会话创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  sessionId:
                    type: string
                    description: 视频观看会话ID
                  reportKey:
                    type: string
                    description: 报告key
                  hasReward:
                    type: boolean
                    description: 该视频是否有奖励
                  stageRanges:
                    type: array
                    description: 观看进度阶段区间定义
                    items:
                      type: object
                      properties:
                        level:
                          type: integer
                          description: '阶段级别，例如：1, 2, 3'
                        name:
                          type: string
                          description: 阶段名称，例如：第一阶段
                        minRatio:
                          type: number
                          format: double
                          description: 最小比例值
                        maxRatio:
                          type: number
                          format: double
                          description: 最大比例值
                        rewardAmount:
                          type: string
                          description: 奖励值
                required:
                  - sessionId
                  - hasReward
                  - rewardStatus
        '400':
          description: 请求参数错误
        '401':
          description: 用户未授权
      security:
        - jwtBearerAuth: []
  /video/events/report:
    post:
      tags:
        - Video
      summary: 上报事件数据
      description: 上报事件数据
      operationId: reportEvents
      security:
        - jwtBearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                sessionId:
                  type: string
                  description: 会话ID
                customerId:
                  type: string
                  description: 用户ID
                signature:
                  type: string
                  description: 数据签名
                clientTimestamp:
                  type: integer
                  format: int64
                  description: 客户端时间戳
                socialPlatform:
                  type: string
                  description: 社交平台
                  enum:
                    - X
                    - YouTube
                socialEvent:
                  type: string
                  description: 社交事件
                  enum:
                    - replay
                    - watch
                events:
                  type: array
                  description: 事件列表
                  items:
                    type: object
                    properties:
                      eventType:
                        type: string
                        description: 事件类型
                      timestamp:
                        type: integer
                        format: int64
                        description: 事件时间戳
                      sequence:
                        type: integer
                        description: 事件序号
                      details:
                        type: object
                        description: 事件数据
                        oneOf:
                          - type: object
                            required:
                              - newState
                              - currentTime
                              - playbackRate
                            properties:
                              newState:
                                type: string
                                enum:
                                  - PLAYING
                                description: 播放状态
                                example: PLAYING
                              currentTime:
                                type: number
                                description: 当前播放时间（秒）
                                example: 120.5
                              playbackRate:
                                type: number
                                description: 播放速率
                                example: 1
                          - type: object
                            required:
                              - newState
                              - currentTime
                            properties:
                              newState:
                                type: string
                                enum:
                                  - PAUSED
                                description: 播放状态
                                example: PAUSED
                              currentTime:
                                type: number
                                description: 当前播放时间（秒）
                                example: 125
                          - type: object
                            required:
                              - currentTime
                              - previousTime
                            properties:
                              currentTime:
                                type: number
                                description: 跳转后播放时间（秒）
                                example: 180
                              previousTime:
                                type: number
                                description: 跳转前播放时间（秒）
                                example: 120.5
                          - type: object
                            required:
                              - tabActive
                              - windowFocused
                            properties:
                              tabActive:
                                type: boolean
                                description: 标签是否活跃
                                example: false
                              windowFocused:
                                type: boolean
                                description: 窗口是否聚焦
                                example: false
                          - type: object
                            required:
                              - tabActive
                              - windowFocused
                            properties:
                              tabActive:
                                type: boolean
                                description: 标签是否活跃
                                example: true
                              windowFocused:
                                type: boolean
                                description: 窗口是否聚焦
                                example: true
                          - type: object
                            required:
                              - state
                            properties:
                              state:
                                type: string
                                enum:
                                  - ACTIVE
                                  - IDLE
                                  - LOCKED
                                description: 用户状态
                                example: IDLE
                deviceFinger:
                  type: string
                  description: 设备指纹
                clientIp:
                  type: string
                  description: 客户端IP
              required:
                - sessionId
                - customerId
                - encryptedData
                - signature
                - clientTimestamp
                - events
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                discriminator:
                  propertyName: code
components:
  securitySchemes:
    jwtBearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        使用JWT Bearer令牌进行身份验证。
        在请求头中添加 `Authorization` 字段，格式为 `Bearer <JWT令牌>`。
