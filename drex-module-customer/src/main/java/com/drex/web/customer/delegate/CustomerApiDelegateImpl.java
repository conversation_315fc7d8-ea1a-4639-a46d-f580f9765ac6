package com.drex.web.customer.delegate;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

import com.drex.core.api.request.SocialConstant;
import com.drex.customer.api.response.CustomerBindDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.web.common.PassportHolder;
import com.drex.web.customer.generated.model.v1.PrivacyAuthResponse;
import com.drex.web.customer.generated.model.v1.PrivacyAuthVO;
import com.drex.web.customer.generated.model.v1.ReservePrivacyAuthResponse;
import com.drex.web.customer.generated.model.v1.ReversePrivacyAuth;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.aliyun.oss.model.PutObjectResult;
import com.drex.activity.task.api.RemoteTaskService;
import com.drex.activity.task.model.response.TaskConfigResponse;
import com.drex.asset.api.model.constant.AssetType;
import com.drex.asset.api.model.request.AssetQueryRequest;
import com.drex.asset.api.service.RemoteAssetService;
import com.drex.core.api.RemoteRexyBasketService;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.RexyBasketsRequest;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.customer.api.RemoteCustomerBindService;
import com.drex.customer.api.RemoteCustomerService;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.web.common.utils.FileUtil;
import com.drex.web.common.utils.UploadAliyunOssUtil;
import com.drex.web.customer.generated.api.v1.CustomerApiDelegate;
import com.drex.web.customer.generated.model.v1.AddDeveloperWaitListUploadResponse;
import com.drex.web.customer.generated.model.v1.AddDeveloperWaitListUploadResponseAllOfObj;
import com.drex.web.customer.generated.model.v1.AddWaitCreatorListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitCreatorListResponse;
import com.drex.web.customer.generated.model.v1.AddWaitDeveloperListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitDeveloperListResponse;
import com.drex.web.customer.generated.model.v1.AddWaitListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitListResponse;
import com.drex.web.customer.generated.model.v1.InviteBindRequest;
import com.drex.web.customer.generated.model.v1.InviteBindResponse;
import com.drex.web.customer.generated.model.v1.InviteInfo;
import com.drex.web.customer.generated.model.v1.InviteInfoResponse;
import com.drex.web.customer.generated.model.v1.SocialUploadRequest;
import com.drex.web.customer.generated.model.v1.SocialUploadResponse;
import com.drex.web.customer.service.AuthService;
import com.drex.web.customer.service.CustomerMapperStruct;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.common.util.BeanUtil;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CustomerApiDelegateImpl implements CustomerApiDelegate {

    @Resource
    private CustomerMapperStruct customerMapperStruct;

    @DubboReference
    private RemoteAssetService remoteAssetService;
    @DubboReference
    private RemoteRexyBasketService remoteRexyBasketService;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @DubboReference
    private RemoteTaskService remoteTaskService;
    @Resource
    private UploadAliyunOssUtil uploadAliyunOssUtil;
    @Resource
    public AuthService authService;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;

    private static final String EMAIL_REGEX =
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    private static final Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_REGEX);

    @Override
    public ResponseEntity<AddWaitListResponse> addWaitList(AddWaitListRequest addWaitListRequest) {
        AddWaitListResponse addWaitListResponse = new AddWaitListResponse();
        addWaitListResponse.setSuccess(true);

        log.info("addWaitListRequest: {}", addWaitListRequest);
        if (addWaitListRequest == null
                || StringUtils.isBlank(addWaitListRequest.getName())
                || StringUtils.isBlank(addWaitListRequest.getEmail())) {
            addWaitListResponse.setSuccess(false);
            addWaitListResponse.setMessage("invalid request, name, project or email is empty");
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }
        String email = addWaitListRequest.getEmail();
        if (!isValidEmail(email)) {
            addWaitListResponse.setSuccess(false);
            addWaitListResponse.setMessage("Invalid email");
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }

        com.drex.customer.api.request.AddWaitListRequest request = new com.drex.customer.api.request.AddWaitListRequest();
        request.setEmail(addWaitListRequest.getEmail());
        request.setProject(addWaitListRequest.getProject());
        request.setEvmAddress(addWaitListRequest.getEvmAddress());
        request.setName(addWaitListRequest.getName());

        Response<Boolean> response = remoteCustomerService.addWaitList(request);
        if (response.isSuccess()) {
            return ResponseEntity.ok(addWaitListResponse);
        } else {
            addWaitListResponse.setSuccess(false);
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }
    }

    /*@Override
    public ResponseEntity<CustomerInfoResponse> customer() {
        CustomerDTO customerDTO = CustomerHolder.customer();
        CustomerInfo customerInfo = customerMapperStruct.toCustomerInfo(customerDTO);

        AssetQueryRequest assetQueryRequest = AssetQueryRequest.builder()
                .customerId(customerDTO.getCustomerId())
                .assetType(AssetType.POINT).build();

        Response<AssetDTO> assetDTOResponse = remoteAssetService.asset(assetQueryRequest);
        if(assetDTOResponse.isSuccess() && assetDTOResponse.getData() != null){
            customerInfo.setPoint(assetDTOResponse.getData().getAvailable().longValue());
        }
        RexyBasketsRequest request = RexyBasketsRequest.builder()
                .customerId(customerDTO.getCustomerId())
                .basketType(RexyConstant.RexyBasketsTypeEnum.normal)
                .build();
        Response<CustomerRexyBasketsDTO> customerRexyBasketsResponse = remoteRexyBasketService.getCustomerRexyBaskets(request);
        if(customerRexyBasketsResponse.isSuccess()){
            CustomerRexyBasketsDTO basketsDTO = customerRexyBasketsResponse.getData();
            CustomerInfoRexy customerInfoRexy = new CustomerInfoRexy();
            customerInfoRexy.setId(basketsDTO.getRexyId());
            customerInfoRexy.setName(basketsDTO.getRexyName());
            customerInfoRexy.setAvatar(basketsDTO.getRexyAvatar());
            customerInfoRexy.setBasketPoint(basketsDTO.getReceived().intValue());
            customerInfoRexy.setRate(basketsDTO.getBasketRate().intValue());
            customerInfoRexy.setLimitPoint(basketsDTO.getBasketLimit().intValue());
            customerInfo.setRexy(customerInfoRexy);
            customerInfo.setAvatar(basketsDTO.getRexyAvatar());
        }

        CustomerInfoResponse customerInfoResponse = new CustomerInfoResponse();
        customerInfoResponse.success();
        customerInfoResponse.setObj(customerInfo);
        return ResponseEntity.ok(customerInfoResponse);
    }*/

    @Override
    public ResponseEntity<InviteInfoResponse> invite() {
        InviteInfo inviteInfo = new InviteInfo();
        Response<TaskConfigResponse> task = remoteTaskService.getTaskByCode("trex", "invite_register", null);
        Response<Long> inviteCountResponse = remoteCustomerService.countByReferrerId(PassportHolder.passport().getPassportId());
        inviteInfo.setInviteCount(inviteCountResponse.isSuccess() ? inviteCountResponse.getData().intValue() : 0);
        inviteInfo.setCustomerId(PassportHolder.passport().getPassportId());
        inviteInfo.setInviteCode(PassportHolder.passport().getReferralCode());
        inviteInfo.setAddress(PassportHolder.passport().getAddress());
        inviteInfo.setInviteRule(task.isSuccess() ? task.getData().getDesc() : "");

        RexyBasketsRequest request = RexyBasketsRequest.builder()
                .customerId(PassportHolder.passport().getPassportId())
                .basketType(RexyConstant.RexyBasketsTypeEnum.invite)
                .build();
        Response<CustomerRexyBasketsDTO> customerRexyBasketsResponse = remoteRexyBasketService.getCustomerRexyBaskets(request);
        if(customerRexyBasketsResponse.isSuccess()){
            inviteInfo.setInviteUnClaimedReward(customerRexyBasketsResponse.getData().getReceived().intValue());
        }
        AssetQueryRequest assetQueryRequest = AssetQueryRequest.builder()
                .customerId(PassportHolder.passport().getPassportId())
                .assetType(AssetType.POINT)
                .build();

        Response<BigDecimal> bigDecimalResponse = remoteAssetService.sumInvitedAsset(assetQueryRequest);
        inviteInfo.setInviteClaimedReward(bigDecimalResponse.isSuccess() ? bigDecimalResponse.getData().intValue() : 0);
        InviteInfoResponse response = new InviteInfoResponse();
        response.success();
        response.setObj(inviteInfo);
        return ResponseEntity.ok(response);
    }

    /**
     * POST /customers/invites/bind : 邀请绑定
     * 邀请绑定
     *
     * @param inviteBindRequest (optional)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<InviteBindResponse> bindInvite(InviteBindRequest inviteBindRequest) {
        log.info("bindInvite:{}", inviteBindRequest);
        InviteBindResponse inviteBindResponse = new InviteBindResponse();
        if(StringUtils.isBlank(inviteBindRequest.getInviteCode())){
            inviteBindResponse.success();
            return ResponseEntity.ok(inviteBindResponse);
        }
        Response<Boolean> response = remoteCustomerService.bindInviteCode(PassportHolder.passport().getPassportId(), inviteBindRequest.getInviteCode());
        if(response.isSuccess()){
            inviteBindResponse.success();
            return ResponseEntity.ok(inviteBindResponse);
        }
        inviteBindResponse.fail(response);
        return ResponseEntity.badRequest().body(inviteBindResponse);
    }

    @Override
    public ResponseEntity<AddWaitCreatorListResponse> addWaitCreatorList(AddWaitCreatorListRequest addWaitCreatorListRequest) {
        AddWaitCreatorListResponse addWaitListResponse = new AddWaitCreatorListResponse();
        addWaitListResponse.setSuccess(true);
        com.drex.customer.api.request.AddWaitCreatorListRequest request = BeanUtil.copyProperties(addWaitCreatorListRequest, new com.drex.customer.api.request.AddWaitCreatorListRequest());
        request.setContentTypes(JSON.toJSONString(addWaitCreatorListRequest.getContentTypes()));
        request.setInterestReasons(JSON.toJSONString(addWaitCreatorListRequest.getInterestReasons()));
        Response<Boolean> response = remoteCustomerService.addWaitCreatorList(request);
        if (response.isSuccess()) {
            return ResponseEntity.ok(addWaitListResponse);
        } else {
            addWaitListResponse.setSuccess(false);
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }
    }

    @Override
    public ResponseEntity<AddWaitDeveloperListResponse> addWaitDeveloperList(AddWaitDeveloperListRequest addWaitDeveloperListRequest) {
        AddWaitDeveloperListResponse addWaitListResponse = new AddWaitDeveloperListResponse();
        addWaitListResponse.setSuccess(true);
        com.drex.customer.api.request.AddWaitDeveloperListRequest request = BeanUtil.copyProperties(addWaitDeveloperListRequest, new com.drex.customer.api.request.AddWaitDeveloperListRequest());
        request.setProjectCategory(JSON.toJSONString(addWaitDeveloperListRequest.getProjectCategory()));
        request.setSupportType(JSON.toJSONString(addWaitDeveloperListRequest.getSupportType()));
        Response<Boolean> response = remoteCustomerService.addWaitDeveloperList(request);
        if (response.isSuccess()) {
            return ResponseEntity.ok(addWaitListResponse);
        } else {
            addWaitListResponse.setSuccess(false);
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }
    }

    /**
     * POST /customers/addDeveloperWaitList/upload : 上传文件
     * 用于上传文件，返回文件名称和文件路径
     *
     * @param file 需要上传的文件 (optional)
     * @return Successful operation (status code 200)
     */
    @Override
    public ResponseEntity<AddDeveloperWaitListUploadResponse> addWaitListUpload(MultipartFile file) {
        File f = FileUtil.toFile(file);
        PutObjectResult result = uploadAliyunOssUtil.putObject("drex/core/wait", f.getName(), f);
        log.info("uploadImage result: {}", result);
        String path = uploadAliyunOssUtil.getLocation("drex/core/wait", f.getName());

        AddDeveloperWaitListUploadResponseAllOfObj obj = new AddDeveloperWaitListUploadResponseAllOfObj();
        obj.setFileName(f.getName());
        obj.setFilePath(path);

        AddDeveloperWaitListUploadResponse response = new AddDeveloperWaitListUploadResponse();
        response.success();
        response.setObj(obj);
        return ResponseEntity.ok(response);
    }

    public static boolean isValidEmail(String email) {
        if (email == null) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * POST /customers/social/upload : /customers/platform/social/upload
     * Upload a social media post
     *
     * @param socialUploadRequest (optional)
     * @return Successful operation (status code 200)
     */
    @Override
    public ResponseEntity<SocialUploadResponse> platformSocialUpload(SocialUploadRequest socialUploadRequest) {
        PassportDTO passport = PassportHolder.passport();
        if(passport != null){
            log.info("customerId:{}, platformSocialUpload:{}", passport.getPassportId(), socialUploadRequest);
        }
        SocialUploadResponse socialUploadResponse = new SocialUploadResponse();
        socialUploadResponse.success();
        return ResponseEntity.ok(socialUploadResponse);
    }

    @Override
    public ResponseEntity<PrivacyAuthResponse> privacyAuth() {
        PassportDTO passport = PassportHolder.passport();
        PrivacyAuthResponse privacyAuthResponse = new PrivacyAuthResponse();
        PrivacyAuthVO privacyAuthVO = new PrivacyAuthVO();

        CustomerBindDTO customerBindX = remoteCustomerBindService.findByCustomerId(passport.getPassportId(), SocialConstant.PlatformEnum.X.name());
        CustomerBindDTO customerBindYoutube = remoteCustomerBindService.findByCustomerId(passport.getPassportId(), SocialConstant.PlatformEnum.YouTube.name());
        if (Objects.nonNull(customerBindX) && customerBindX.getPrivacyAuth()) {
            privacyAuthVO.setPrivacyAuthX(true);
        }
        if (Objects.nonNull(customerBindYoutube) && customerBindYoutube.getPrivacyAuth()) {
            privacyAuthVO.setPrivacyAuthYoutube(true);
        }
        privacyAuthResponse.setObj(privacyAuthVO);
        privacyAuthResponse.success();
        return ResponseEntity.ok(privacyAuthResponse);
    }

    @Override
    public ResponseEntity<ReservePrivacyAuthResponse> reversePrivacyAuth(ReversePrivacyAuth reversePrivacyAuth) {
        PassportDTO passport = PassportHolder.passport();
        ReservePrivacyAuthResponse reservePrivacyAuthResponse = new ReservePrivacyAuthResponse();
        log.info("customerId:{}, reservePrivacyAuth:{}", passport.getPassportId(), reversePrivacyAuth.getPlatform());

        boolean b = remoteCustomerBindService.reservePrivacyAuth(passport.getPassportId(), reversePrivacyAuth.getPlatform());
        if (b) {
            reservePrivacyAuthResponse.setObj(true);
            reservePrivacyAuthResponse.success();
        }
        return ResponseEntity.ok(reservePrivacyAuthResponse);
    }
}
